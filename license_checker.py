import os
import sys
import firebase_admin
from firebase_admin import credentials, firestore
import datetime
import webbrowser  # Opens the WhatsApp link
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from ttkbootstrap.dialogs import Messagebox
import uuid
import getpass
import subprocess
import hashlib
import json
from google.oauth2 import service_account
from google.auth.transport.requests import AuthorizedSession
import time
import platform
import psutil
import ctypes
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import random
import threading
from security_protection import get_security_instance, protection_check, secure_string, unsecure_string
from license_validation import enhanced_license_check

def resource_path(relative_path):
    """ Get absolute path to resource, works for both development & PyInstaller EXE """
    if getattr(sys, 'frozen', False):  # Running as an EXE
        base_path = sys._MEIPASS
    else:
        base_path = os.path.dirname(__file__)  # Running as Python script
    return os.path.join(base_path, relative_path)

# Fix Firebase JSON Path
firebase_json_path = resource_path("meta-master-firebase.json")

SCOPES = ["https://www.googleapis.com/auth/cloud-platform"]
SERVICE_ACCOUNT_FILE = resource_path("meta-master-firebase.json")

credentials = service_account.Credentials.from_service_account_file(
    SERVICE_ACCOUNT_FILE,
    scopes=SCOPES
)
authorized_session = AuthorizedSession(credentials)

# Extract project_id from the service account file
with open(SERVICE_ACCOUNT_FILE, "r") as f:
    service_account_data = json.load(f)
project_id = service_account_data["project_id"]

# Avoid repeated Firebase initialization
if not firebase_admin._apps:
    try:
        firebase_admin.initialize_app(credentials, {"projectId": project_id})
    except Exception as e:
        Messagebox.show_error("Firebase Error", f"Firebase connection failed:\n{e}")
        sys.exit()

db = firestore.client()

APPDATA_DIR = os.path.join(os.getenv("APPDATA"), "MetaMaster")
os.makedirs(APPDATA_DIR, exist_ok=True)

LICENSE_FILE = os.path.join(APPDATA_DIR, "license.txt")
DEVICE_ID_FILE = os.path.join(APPDATA_DIR, "device_id.txt")

def save_license_key(license_key):
    """Save the license key in a writable directory (AppData)."""
    os.makedirs(os.path.dirname(LICENSE_FILE), exist_ok=True)  # Ensure folder exists
    with open(LICENSE_FILE, "w") as f:
        f.write(license_key)

def load_license_key():
    """Retrieve the saved license key from the file."""
    if os.path.exists(LICENSE_FILE):
        with open(LICENSE_FILE, "r") as f:
            return f.read().strip()
    return None

def get_stable_device_id():
    """Generate a stable device ID using multiple hardware identifiers with enhanced security."""
    try:
        # Verify protection is active
        protection_check()

        # Get multiple hardware identifiers
        identifiers = []

        # Get motherboard serial
        try:
            motherboard_cmd = 'wmic baseboard get serialnumber'
            motherboard_serial = subprocess.check_output(motherboard_cmd, shell=True, timeout=10).decode().split('\n')[1].strip()
            if motherboard_serial and motherboard_serial != "SerialNumber":
                identifiers.append(f"mb_{motherboard_serial}")
        except Exception:
            pass

        # Get disk serial
        try:
            disk_cmd = 'wmic diskdrive get serialnumber'
            disk_output = subprocess.check_output(disk_cmd, shell=True, timeout=10).decode()
            disk_lines = [line.strip() for line in disk_output.split('\n') if line.strip() and line.strip() != "SerialNumber"]
            if disk_lines:
                identifiers.append(f"disk_{disk_lines[0]}")
        except Exception:
            pass

        # Get CPU ID
        try:
            cpu_cmd = 'wmic cpu get processorid'
            cpu_id = subprocess.check_output(cpu_cmd, shell=True, timeout=10).decode().split('\n')[1].strip()
            if cpu_id and cpu_id != "ProcessorId":
                identifiers.append(f"cpu_{cpu_id}")
        except Exception:
            pass

        # Get BIOS serial
        try:
            bios_cmd = 'wmic bios get serialnumber'
            bios_serial = subprocess.check_output(bios_cmd, shell=True, timeout=10).decode().split('\n')[1].strip()
            if bios_serial and bios_serial != "SerialNumber":
                identifiers.append(f"bios_{bios_serial}")
        except Exception:
            pass

        # Get MAC address
        try:
            import uuid
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) for elements in range(0,2*6,2)][::-1])
            identifiers.append(f"mac_{mac}")
        except Exception:
            pass

        # Combine all identifiers
        if identifiers:
            combined = "_".join(identifiers)
            # Add system-specific salt
            salt = f"{platform.machine()}_{platform.processor()}"
            final_string = f"{combined}_{salt}_metamaster_2024"

            # Create multiple hash layers for security
            hash1 = hashlib.sha256(final_string.encode()).hexdigest()
            hash2 = hashlib.sha512(hash1.encode()).hexdigest()
            final_hash = hashlib.sha256(hash2.encode()).hexdigest()

            return final_hash
        else:
            # Fallback to system-based ID
            fallback = f"{platform.machine()}_{platform.processor()}_{os.environ.get('COMPUTERNAME', 'UNKNOWN')}"
            return hashlib.sha256(fallback.encode()).hexdigest()

    except Exception as e:
        print(f"[Device ID Error] {e}")
        # Even in error, return a consistent fallback
        fallback = f"FALLBACK_{platform.machine()}_{hashlib.md5(str(time.time()).encode()).hexdigest()[:8]}"
        return hashlib.sha256(fallback.encode()).hexdigest()

def save_device_id(id_str):
    """Save the generated device ID locally."""
    os.makedirs(os.path.dirname(DEVICE_ID_FILE), exist_ok=True)
    with open(DEVICE_ID_FILE, "w") as f:
        f.write(id_str)

def load_device_id():
    """Load the saved device ID from local storage."""
    if os.path.exists(DEVICE_ID_FILE):
        with open(DEVICE_ID_FILE, "r") as f:
            return f.read().strip()
    return None

def check_license(license_key=None):
    """Validate the license key with Firebase Firestore and enforce single-device restriction with enhanced security."""
    try:
        # Verify protection is active
        protection_check()

        # Add random delay to prevent timing attacks
        time.sleep(random.uniform(0.1, 0.5))

        if license_key is None:
            license_key = load_license_key()

        if not license_key:
            return False, "No License Key Found!"

        # Verify execution environment
        security = get_security_instance()
        env_valid, env_message = security.verify_execution_environment()
        if not env_valid:
            return False, f"❌ Security check failed: {env_message}"

        # Enhanced license validation with online verification
        online_valid, online_message = enhanced_license_check(license_key)

        if online_valid:
            return True, online_message

        # Fallback to Firebase validation if online validation fails
        doc_ref = db.collection("licenses").document(license_key)

        try:
            doc = doc_ref.get(timeout=15)  # Increased timeout for security checks
        except Exception as e:
            return False, f"❌ License validation failed: {str(e)}"

        if doc.exists:
            license_data = doc.to_dict()
            is_active = license_data.get("active", False)
            expiry_date = license_data.get("expiry")
            registered_device = license_data.get("device_id", None)
            max_activations = license_data.get("max_activations", 1)
            current_activations = license_data.get("current_activations", 0)
            last_check = license_data.get("last_check", None)

            # Convert expiry date to datetime object
            try:
                expiry_date_dt = datetime.datetime.strptime(expiry_date, "%Y-%m-%d")
                today = datetime.datetime.today()
                remaining_days = (expiry_date_dt - today).days
            except Exception:
                return False, "❌ Invalid license format!"

            stored_device_id = load_device_id()
            current_device_id = get_stable_device_id()

            # Enhanced security checks
            if not is_active:
                return False, "❌ This license has been deactivated!"

            if remaining_days <= 0:
                return False, "❌ License Expired! Please renew."

            # Check for suspicious rapid validation attempts
            if last_check:
                try:
                    last_check_dt = datetime.datetime.fromisoformat(last_check)
                    time_diff = (datetime.datetime.now() - last_check_dt).total_seconds()
                    if time_diff < 5:  # Less than 5 seconds since last check
                        return False, "❌ Too many validation attempts!"
                except Exception:
                    pass

            # Update last check timestamp
            doc_ref.update({
                "last_check": datetime.datetime.now().isoformat(),
                "last_ip": "local_check"  # Could be enhanced with actual IP
            })

            if registered_device is None:
                # First-time activation: Bind license to this device
                if current_activations >= max_activations:
                    return False, "❌ Maximum activations reached!"

                doc_ref.update({
                    "device_id": current_device_id,
                    "current_activations": current_activations + 1,
                    "activation_date": datetime.datetime.now().isoformat()
                })
                save_device_id(current_device_id)
                return True, "✅ First-time activation successful."

            if stored_device_id == registered_device:
                return True, "✅ License is valid (local verification)."

            if current_device_id == registered_device:
                save_device_id(current_device_id)
                return True, "✅ License verified for this device."

            return False, "❌ License is registered to a different device!"
        else:
            return False, "❌ Invalid License Key! Contact support."

    except Exception as e:
        # Log security event
        try:
            security = get_security_instance()
            security._log_security_event(f"License check error: {str(e)}")
        except Exception:
            pass
        return False, f"❌ License validation error: {str(e)}"

def activate_license(root, entry):
    """Allow the user to enter a license key, validate it, and restrict to one device."""
    license_key = entry.get().strip()
    current_device = get_stable_device_id()  # Get unique device ID

    print(f"🖥️ Device ID Detected: {current_device}")

    try:
        doc_ref = db.collection("licenses").document(license_key)
        doc = doc_ref.get(timeout=10)  # 10 second timeout
    except Exception as e:
        Messagebox.show_error("Activation Error", f"Failed to check license: {e}")
        return

    is_valid, message = check_license(license_key)

    if is_valid:
        # Save the license key locally
        save_license_key(license_key)

        # Update the license in Firebase with the device ID
        doc_ref.update({"device_id": current_device})  # Bind the license to this device

        Messagebox.show_info("🎉 Activation Successful!\n" + message, "License Activated")
        root.destroy()  # Close the License Window
    else:
        Messagebox.show_error(message, "Activation Failed")

def show_license_input_window():
    """Display a window to input the license key."""
    root = ttk.Window(themename="superhero")
    root.title("License Activation")
    root.geometry("400x250")
    root.resizable(False, False)

    ttk.Label(root, text="Enter Your License Key:", font=("Arial", 12, "bold")).pack(pady=10)
    entry = ttk.Entry(root, width=40)
    entry.pack(pady=5)

    btn_frame = ttk.Frame(root)
    btn_frame.pack(pady=10)

    # Create the "Contact Us" button and make it always visible
    ttk.Button(
        btn_frame, 
        text="Contact Us", 
        bootstyle=INFO, 
        command=lambda: webbrowser.open("https://www.facebook.com/m.mastersoft")
    ).pack(side=LEFT, padx=5)

    ttk.Button(
        btn_frame, 
        text="Activate", 
        bootstyle=SUCCESS, 
        command=lambda: activate_license(root, entry)
    ).pack(side=LEFT, padx=5)

    root.mainloop()

# Run License Check and Open License Window If Needed
if not check_license()[0]:  
    show_license_input_window()
