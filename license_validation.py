"""
Enhanced License Validation System for Meta Master
Implements server-side validation with encrypted communication
"""

import os
import sys
import time
import json
import hashlib
import platform
import requests
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from security_protection import get_security_instance, protection_check
from secure_config import get_secure_config

class EnhancedLicenseValidator:
    def __init__(self):
        self._security = get_security_instance()
        self._config = get_secure_config()
        self._validation_key = self._generate_validation_key()
        self._cipher = Fernet(self._validation_key)
        
        # Server endpoints (these would be your actual license server URLs)
        self._validation_endpoints = [
            "https://api.getmetamaster.com/validate",
            "https://backup-api.getmetamaster.com/validate",
            "https://license.getmetamaster.com/check"
        ]
        
    def _generate_validation_key(self):
        """Generate encryption key for license validation"""
        try:
            # Use a combination of system info and app-specific salt
            system_info = f"{platform.machine()}{platform.processor()}"
            app_salt = "metamaster_license_validation_2024"
            combined = f"{system_info}_{app_salt}".encode()
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=b'license_validation_salt_2024',
                iterations=200000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(combined))
            return key
        except Exception:
            # Fallback key
            fallback = "metamaster_license_fallback_2024"
            return base64.urlsafe_b64encode(hashlib.sha256(fallback.encode()).digest())
    
    def _encrypt_validation_data(self, data):
        """Encrypt validation data for transmission"""
        try:
            if isinstance(data, dict):
                data = json.dumps(data)
            if isinstance(data, str):
                data = data.encode()
            return self._cipher.encrypt(data)
        except Exception:
            return data
    
    def _decrypt_validation_response(self, encrypted_data):
        """Decrypt validation response"""
        try:
            if isinstance(encrypted_data, str):
                encrypted_data = encrypted_data.encode()
            decrypted = self._cipher.decrypt(encrypted_data)
            return json.loads(decrypted.decode())
        except Exception:
            return None
    
    def _generate_hardware_fingerprint(self):
        """Generate comprehensive hardware fingerprint"""
        try:
            import subprocess
            import uuid
            
            fingerprint_data = {}
            
            # Get motherboard serial
            try:
                mb_result = subprocess.run(['wmic', 'baseboard', 'get', 'serialnumber'], 
                                         capture_output=True, text=True, timeout=10)
                mb_serial = mb_result.stdout.split('\n')[1].strip()
                if mb_serial and mb_serial != "SerialNumber":
                    fingerprint_data['motherboard'] = mb_serial
            except Exception:
                pass
            
            # Get CPU ID
            try:
                cpu_result = subprocess.run(['wmic', 'cpu', 'get', 'processorid'], 
                                          capture_output=True, text=True, timeout=10)
                cpu_id = cpu_result.stdout.split('\n')[1].strip()
                if cpu_id and cpu_id != "ProcessorId":
                    fingerprint_data['cpu'] = cpu_id
            except Exception:
                pass
            
            # Get BIOS serial
            try:
                bios_result = subprocess.run(['wmic', 'bios', 'get', 'serialnumber'], 
                                           capture_output=True, text=True, timeout=10)
                bios_serial = bios_result.stdout.split('\n')[1].strip()
                if bios_serial and bios_serial != "SerialNumber":
                    fingerprint_data['bios'] = bios_serial
            except Exception:
                pass
            
            # Get MAC address
            try:
                mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                               for elements in range(0,2*6,2)][::-1])
                fingerprint_data['mac'] = mac
            except Exception:
                pass
            
            # Get disk serial
            try:
                disk_result = subprocess.run(['wmic', 'diskdrive', 'get', 'serialnumber'], 
                                           capture_output=True, text=True, timeout=10)
                disk_lines = [line.strip() for line in disk_result.stdout.split('\n') 
                             if line.strip() and line.strip() != "SerialNumber"]
                if disk_lines:
                    fingerprint_data['disk'] = disk_lines[0]
            except Exception:
                pass
            
            # Add system information
            fingerprint_data['platform'] = platform.platform()
            fingerprint_data['machine'] = platform.machine()
            fingerprint_data['processor'] = platform.processor()
            
            # Create hash of all fingerprint data
            fingerprint_str = json.dumps(fingerprint_data, sort_keys=True)
            fingerprint_hash = hashlib.sha256(fingerprint_str.encode()).hexdigest()
            
            return {
                'fingerprint': fingerprint_hash,
                'components': fingerprint_data,
                'timestamp': time.time()
            }
            
        except Exception as e:
            # Fallback fingerprint
            fallback_data = {
                'platform': platform.platform(),
                'machine': platform.machine(),
                'fallback': True,
                'timestamp': time.time()
            }
            fallback_str = json.dumps(fallback_data, sort_keys=True)
            fallback_hash = hashlib.sha256(fallback_str.encode()).hexdigest()
            
            return {
                'fingerprint': fallback_hash,
                'components': fallback_data,
                'timestamp': time.time()
            }
    
    def validate_license_online(self, license_key):
        """Validate license with online server"""
        try:
            protection_check()
            
            # Generate hardware fingerprint
            hw_fingerprint = self._generate_hardware_fingerprint()
            
            # Prepare validation data
            validation_data = {
                'license_key': license_key,
                'hardware_fingerprint': hw_fingerprint,
                'app_version': '5.3.2',
                'timestamp': time.time(),
                'validation_id': hashlib.md5(f"{license_key}_{time.time()}".encode()).hexdigest()
            }
            
            # Encrypt validation data
            encrypted_data = self._encrypt_validation_data(validation_data)
            
            # Try each validation endpoint
            for endpoint in self._validation_endpoints:
                try:
                    response = requests.post(
                        endpoint,
                        data={'validation_data': base64.b64encode(encrypted_data).decode()},
                        timeout=15,
                        headers={
                            'User-Agent': 'MetaMaster/5.3.2',
                            'Content-Type': 'application/x-www-form-urlencoded'
                        }
                    )
                    
                    if response.status_code == 200:
                        # Decrypt and parse response
                        response_data = self._decrypt_validation_response(
                            base64.b64decode(response.text)
                        )
                        
                        if response_data:
                            return self._process_validation_response(response_data)
                    
                except requests.RequestException:
                    continue
            
            # If all endpoints fail, return offline validation result
            return False, "Online validation failed. Please check your internet connection."
            
        except Exception as e:
            return False, f"License validation error: {str(e)}"
    
    def _process_validation_response(self, response_data):
        """Process validation response from server"""
        try:
            if not isinstance(response_data, dict):
                return False, "Invalid response format"
            
            status = response_data.get('status', 'invalid')
            message = response_data.get('message', 'Unknown error')
            
            if status == 'valid':
                # Store validation cache
                self._store_validation_cache(response_data)
                return True, message
            elif status == 'expired':
                return False, "License has expired"
            elif status == 'invalid':
                return False, "Invalid license key"
            elif status == 'device_mismatch':
                return False, "License is registered to a different device"
            elif status == 'suspended':
                return False, "License has been suspended"
            else:
                return False, message
                
        except Exception:
            return False, "Error processing validation response"
    
    def _store_validation_cache(self, validation_data):
        """Store validation cache for offline use"""
        try:
            cache_data = {
                'validation_time': time.time(),
                'expires_at': validation_data.get('expires_at', time.time() + 86400),  # 24 hours default
                'license_status': validation_data.get('status'),
                'device_fingerprint': self._generate_hardware_fingerprint()['fingerprint']
            }
            
            self._config.set_secure_value('license_cache', cache_data)
            
        except Exception:
            pass
    
    def validate_license_offline(self, license_key):
        """Validate license using cached data"""
        try:
            protection_check()
            
            cache_data = self._config.get_secure_value('license_cache')
            
            if not cache_data:
                return False, "No offline validation data available"
            
            current_time = time.time()
            expires_at = cache_data.get('expires_at', 0)
            
            if current_time > expires_at:
                return False, "Offline validation cache has expired"
            
            # Verify device fingerprint
            current_fingerprint = self._generate_hardware_fingerprint()['fingerprint']
            cached_fingerprint = cache_data.get('device_fingerprint')
            
            if current_fingerprint != cached_fingerprint:
                return False, "Device fingerprint mismatch"
            
            license_status = cache_data.get('license_status')
            
            if license_status == 'valid':
                return True, "License validated offline"
            else:
                return False, "Cached license status is invalid"
                
        except Exception:
            return False, "Offline validation failed"
    
    def comprehensive_license_validation(self, license_key):
        """Perform comprehensive license validation (online + offline fallback)"""
        try:
            # First try online validation
            online_valid, online_message = self.validate_license_online(license_key)
            
            if online_valid:
                return True, online_message
            
            # If online fails, try offline validation
            offline_valid, offline_message = self.validate_license_offline(license_key)
            
            if offline_valid:
                return True, f"{offline_message} (offline mode)"
            
            # Both failed
            return False, f"Validation failed: {online_message}"
            
        except Exception as e:
            return False, f"License validation error: {str(e)}"

# Global validator instance
_license_validator = None

def get_license_validator():
    """Get or create license validator instance"""
    global _license_validator
    if _license_validator is None:
        _license_validator = EnhancedLicenseValidator()
    return _license_validator

def enhanced_license_check(license_key):
    """Enhanced license checking with online validation"""
    try:
        validator = get_license_validator()
        return validator.comprehensive_license_validation(license_key)
    except Exception as e:
        return False, f"License check failed: {str(e)}"
