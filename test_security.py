"""
Test script to verify Meta Master security implementation
"""

import sys
import os

def test_security_imports():
    """Test if all security modules can be imported"""
    print("🔍 Testing security module imports...")
    
    try:
        from security_protection import get_security_instance, protection_check
        print("   ✅ security_protection imported successfully")
    except Exception as e:
        print(f"   ❌ security_protection import failed: {e}")
        return False
    
    try:
        from secure_config import get_secure_config, get_secure_credentials
        print("   ✅ secure_config imported successfully")
    except Exception as e:
        print(f"   ❌ secure_config import failed: {e}")
        return False
    
    try:
        from license_checker import check_license, get_stable_device_id, LICENSE_FILE
        print("   ✅ license_checker imported successfully")
    except Exception as e:
        print(f"   ❌ license_checker import failed: {e}")
        return False
    
    try:
        from license_validation import enhanced_license_check
        print("   ✅ license_validation imported successfully")
    except Exception as e:
        print(f"   ❌ license_validation import failed: {e}")
        return False
    
    try:
        from runtime_protection import runtime_protect, initialize_runtime_protection
        print("   ✅ runtime_protection imported successfully")
    except Exception as e:
        print(f"   ❌ runtime_protection import failed: {e}")
        return False
    
    try:
        from integrity_verification import initialize_integrity_verification
        print("   ✅ integrity_verification imported successfully")
    except Exception as e:
        print(f"   ❌ integrity_verification import failed: {e}")
        return False
    
    return True

def test_security_initialization():
    """Test security system initialization"""
    print("\n🔧 Testing security initialization...")
    
    try:
        from security_protection import get_security_instance
        security = get_security_instance()
        print("   ✅ Security instance created successfully")
        
        # Test environment verification
        env_valid, env_message = security.verify_execution_environment()
        print(f"   📋 Environment check: {env_message}")
        
        return True
    except Exception as e:
        print(f"   ❌ Security initialization failed: {e}")
        return False

def test_license_system():
    """Test license validation system"""
    print("\n🔑 Testing license system...")
    
    try:
        from license_checker import get_stable_device_id, LICENSE_FILE
        
        # Test device ID generation
        device_id = get_stable_device_id()
        print(f"   📱 Device ID generated: {device_id[:16]}...")
        
        # Test license file path
        print(f"   📁 License file path: {LICENSE_FILE}")
        
        return True
    except Exception as e:
        print(f"   ❌ License system test failed: {e}")
        return False

def test_secure_config():
    """Test secure configuration system"""
    print("\n⚙️ Testing secure configuration...")
    
    try:
        from secure_config import get_secure_config
        
        config = get_secure_config()
        
        # Test saving and loading a test value
        test_key = "test_security_key"
        test_value = "test_security_value"
        
        success = config.set_secure_value(test_key, test_value)
        if success:
            print("   ✅ Secure config save successful")
        else:
            print("   ⚠️ Secure config save failed")
        
        retrieved_value = config.get_secure_value(test_key)
        if retrieved_value == test_value:
            print("   ✅ Secure config load successful")
        else:
            print("   ⚠️ Secure config load failed")
        
        return True
    except Exception as e:
        print(f"   ❌ Secure config test failed: {e}")
        return False

def test_runtime_protection():
    """Test runtime protection system"""
    print("\n🛡️ Testing runtime protection...")
    
    try:
        from runtime_protection import runtime_protect, initialize_runtime_protection
        
        # Test initialization
        if initialize_runtime_protection():
            print("   ✅ Runtime protection initialized successfully")
        else:
            print("   ⚠️ Runtime protection initialization failed")
        
        # Test function protection decorator
        @runtime_protect
        def test_protected_function():
            return "protected_result"
        
        result = test_protected_function()
        if result == "protected_result":
            print("   ✅ Function protection working")
        else:
            print("   ⚠️ Function protection may have issues")
        
        return True
    except Exception as e:
        print(f"   ❌ Runtime protection test failed: {e}")
        return False

def test_integrity_verification():
    """Test integrity verification system"""
    print("\n🔍 Testing integrity verification...")
    
    try:
        from integrity_verification import initialize_integrity_verification, verify_application_integrity
        
        # Test initialization
        if initialize_integrity_verification():
            print("   ✅ Integrity verification initialized successfully")
        else:
            print("   ⚠️ Integrity verification initialization failed")
        
        # Test application integrity check
        is_valid, message = verify_application_integrity()
        print(f"   📋 Integrity check: {message}")
        
        return True
    except Exception as e:
        print(f"   ❌ Integrity verification test failed: {e}")
        return False

def main():
    """Run all security tests"""
    print("🚀 Meta Master Security System Test")
    print("=" * 50)
    
    tests = [
        ("Security Imports", test_security_imports),
        ("Security Initialization", test_security_initialization),
        ("License System", test_license_system),
        ("Secure Configuration", test_secure_config),
        ("Runtime Protection", test_runtime_protection),
        ("Integrity Verification", test_integrity_verification)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"   ❌ {test_name} test failed")
        except Exception as e:
            print(f"   ❌ {test_name} test error: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All security tests passed! The system is ready.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
