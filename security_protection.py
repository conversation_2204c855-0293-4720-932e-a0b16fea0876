"""
Advanced Security Protection Module for Meta Master
Implements multiple layers of protection against reverse engineering and cracking
"""

import os
import sys
import time
import hashlib
import platform
import ctypes
import threading
import subprocess
import psutil
import random
import string
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import zlib
import marshal
import inspect
import gc

class SecurityProtection:
    def __init__(self):
        self._protection_active = True
        self._integrity_hash = None
        self._start_time = time.time()
        self._debug_detected = False
        self._tamper_detected = False
        self._key = self._generate_protection_key()
        self._cipher = Fernet(self._key)
        
        # Start protection threads
        self._start_protection_threads()
    
    def _generate_protection_key(self):
        """Generate encryption key based on system characteristics"""
        try:
            # Combine multiple system identifiers
            system_info = f"{platform.machine()}{platform.processor()}{os.environ.get('COMPUTERNAME', '')}"
            password = system_info.encode()
            salt = b'metamaster_security_salt_2024'
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))
            return key
        except Exception:
            # Fallback key if system info fails
            return base64.urlsafe_b64encode(b'fallback_metamaster_key_2024_secure')
    
    def encrypt_string(self, data):
        """Encrypt sensitive strings"""
        try:
            if isinstance(data, str):
                data = data.encode()
            return self._cipher.encrypt(data)
        except Exception:
            return data
    
    def decrypt_string(self, encrypted_data):
        """Decrypt sensitive strings"""
        try:
            if isinstance(encrypted_data, bytes):
                return self._cipher.decrypt(encrypted_data).decode()
            return encrypted_data
        except Exception:
            return encrypted_data
    
    def _start_protection_threads(self):
        """Start background protection monitoring"""
        threading.Thread(target=self._anti_debug_monitor, daemon=True).start()
        threading.Thread(target=self._integrity_monitor, daemon=True).start()
        threading.Thread(target=self._process_monitor, daemon=True).start()
    
    def _anti_debug_monitor(self):
        """Monitor for debugging attempts"""
        while self._protection_active:
            try:
                # Check for common debuggers
                debugger_processes = [
                    'ollydbg.exe', 'x64dbg.exe', 'windbg.exe', 'ida.exe', 'ida64.exe',
                    'cheatengine.exe', 'processhacker.exe', 'procmon.exe', 'wireshark.exe',
                    'fiddler.exe', 'charles.exe', 'burpsuite.exe', 'ghidra.exe'
                ]
                
                for proc in psutil.process_iter(['name']):
                    try:
                        if proc.info['name'].lower() in debugger_processes:
                            self._debug_detected = True
                            self._trigger_protection_response("Debugger detected")
                            return
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                
                # Check for debugging flags
                if ctypes.windll.kernel32.IsDebuggerPresent():
                    self._debug_detected = True
                    self._trigger_protection_response("Debugger attached")
                    return
                
                time.sleep(2)
            except Exception:
                time.sleep(5)
    
    def _integrity_monitor(self):
        """Monitor application integrity"""
        while self._protection_active:
            try:
                # Check if main executable has been modified
                if hasattr(sys, 'frozen'):
                    exe_path = sys.executable
                    if os.path.exists(exe_path):
                        with open(exe_path, 'rb') as f:
                            current_hash = hashlib.sha256(f.read()).hexdigest()
                        
                        if self._integrity_hash is None:
                            self._integrity_hash = current_hash
                        elif self._integrity_hash != current_hash:
                            self._tamper_detected = True
                            self._trigger_protection_response("File integrity compromised")
                            return
                
                time.sleep(10)
            except Exception:
                time.sleep(15)
    
    def _process_monitor(self):
        """Monitor for suspicious processes and memory access"""
        while self._protection_active:
            try:
                current_process = psutil.Process()
                
                # Check for memory dumps
                if current_process.memory_info().rss > 500 * 1024 * 1024:  # 500MB
                    suspicious_tools = ['processdump', 'procdump', 'memorydump']
                    for proc in psutil.process_iter(['name']):
                        try:
                            if any(tool in proc.info['name'].lower() for tool in suspicious_tools):
                                self._trigger_protection_response("Memory dump detected")
                                return
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            continue
                
                time.sleep(5)
            except Exception:
                time.sleep(10)
    
    def _trigger_protection_response(self, reason):
        """Trigger protection response when threat detected"""
        try:
            # Log the security event
            self._log_security_event(reason)
            
            # Graceful shutdown with misleading error
            import tkinter.messagebox as msgbox
            msgbox.showerror("System Error", 
                           "A critical system error has occurred. The application will now close.\n"
                           "Error Code: 0x80070005")
            
            # Clear sensitive data from memory
            self._clear_sensitive_memory()
            
            # Exit application
            os._exit(1)
        except Exception:
            os._exit(1)
    
    def _log_security_event(self, reason):
        """Log security events for analysis"""
        try:
            log_dir = os.path.join(os.getenv("TEMP"), ".metamaster_logs")
            os.makedirs(log_dir, exist_ok=True)
            
            log_file = os.path.join(log_dir, "security.log")
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            
            with open(log_file, "a") as f:
                f.write(f"{timestamp} - Security Event: {reason}\n")
        except Exception:
            pass
    
    def _clear_sensitive_memory(self):
        """Clear sensitive data from memory"""
        try:
            # Force garbage collection
            gc.collect()
            
            # Overwrite sensitive variables
            if hasattr(self, '_key'):
                self._key = b'0' * len(self._key)
            if hasattr(self, '_cipher'):
                del self._cipher
                
        except Exception:
            pass
    
    def verify_execution_environment(self):
        """Verify the application is running in a legitimate environment"""
        try:
            # Check if running from expected location
            if hasattr(sys, 'frozen'):
                exe_path = sys.executable
                if 'temp' in exe_path.lower() or 'tmp' in exe_path.lower():
                    return False, "Suspicious execution location"
            
            # Check for virtual machine indicators
            vm_indicators = [
                'vmware', 'virtualbox', 'vbox', 'qemu', 'xen', 'hyper-v'
            ]
            
            system_info = platform.platform().lower()
            for indicator in vm_indicators:
                if indicator in system_info:
                    return False, "Virtual machine detected"
            
            # Check system uptime (VMs often have low uptime)
            uptime = time.time() - psutil.boot_time()
            if uptime < 300:  # Less than 5 minutes
                return False, "Suspicious system uptime"
            
            return True, "Environment verified"
            
        except Exception:
            return False, "Environment verification failed"
    
    def obfuscate_code_flow(self, func):
        """Decorator to obfuscate function execution flow"""
        def wrapper(*args, **kwargs):
            # Add random delays
            time.sleep(random.uniform(0.001, 0.01))
            
            # Check protection status
            if not self._protection_active:
                return None
            
            # Execute with dummy operations
            dummy_ops = [
                lambda: sum(range(random.randint(1, 100))),
                lambda: ''.join(random.choices(string.ascii_letters, k=10)),
                lambda: hashlib.md5(str(time.time()).encode()).hexdigest()
            ]
            
            random.choice(dummy_ops)()
            
            result = func(*args, **kwargs)
            
            # More dummy operations
            random.choice(dummy_ops)()
            
            return result
        return wrapper
    
    def is_protection_active(self):
        """Check if protection is still active"""
        return self._protection_active and not self._debug_detected and not self._tamper_detected

# Global security instance
_security_instance = None

def get_security_instance():
    """Get or create security protection instance"""
    global _security_instance
    if _security_instance is None:
        _security_instance = SecurityProtection()
    return _security_instance

def secure_string(data):
    """Encrypt a string for storage"""
    return get_security_instance().encrypt_string(data)

def unsecure_string(encrypted_data):
    """Decrypt a string from storage"""
    return get_security_instance().decrypt_string(encrypted_data)

def protection_check():
    """Quick protection status check"""
    security = get_security_instance()
    if not security.is_protection_active():
        os._exit(1)
    return True
