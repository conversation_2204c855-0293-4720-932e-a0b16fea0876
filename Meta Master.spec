# -*- mode: python ; coding: utf-8 -*-
# Enhanced PyInstaller spec with security features for Meta Master

import os
import sys

# Security-enhanced Analysis configuration
a = Analysis(
    ['E:\\Software Buid\\Final Meta Master With License\\Meta Master.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('E:\\Software Buid\\Final Meta Master With License\\Meta Master.ico', '.'),
        ('E:\\Software Buid\\Final Meta Master With License\\Meta Master.png', '.'),
        ('E:\\Software Buid\\Final Meta Master With License\\meta-master-firebase.json', '.'),
        ('E:\\Software Buid\\Final Meta Master With License\\README.txt', '.'),
        ('E:\\Software Buid\\Final Meta Master With License\\exiftool.exe', '.'),
        ('E:\\Software Buid\\Final Meta Master With License\\exiftool_files', 'exiftool_files'),
        ('E:\\Software Buid\\Final Meta Master With License\\security_protection.py', '.'),
        ('E:\\Software Buid\\Final Meta Master With License\\secure_config.py', '.'),
        ('E:\\Software Buid\\Final Meta Master With License\\license_checker.py', '.'),
        ('E:\\Software Buid\\Final Meta Master With License\\license_validation.py', '.'),
        ('E:\\Software Buid\\Final Meta Master With License\\runtime_protection.py', '.'),
        ('E:\\Software Buid\\Final Meta Master With License\\integrity_verification.py', '.')
    ],
    hiddenimports=[
        'ttkbootstrap',
        'cv2',
        'numpy',
        'cryptography',
        'cryptography.fernet',
        'cryptography.hazmat.primitives',
        'cryptography.hazmat.primitives.kdf.pbkdf2',
        'cryptography.hazmat.primitives.hashes',
        'psutil',
        'security_protection',
        'secure_config',
        'license_checker'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter.test',
        'unittest',
        'pdb',
        'doctest',
        'difflib',
        'inspect',
        'pydoc',
        'dis'
    ],
    noarchive=True,  # Changed to True for better security
    optimize=2,      # Maximum optimization to remove debug info
)

# Enhanced PYZ with compression and obfuscation
pyz = PYZ(
    a.pure,
    a.zipped_data,
    cipher=None  # Could add encryption here if needed
)

# Security-enhanced EXE configuration
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Meta Master',
    debug=False,                        # Disable debug mode
    bootloader_ignore_signals=False,
    strip=True,                         # Strip debug symbols
    upx=True,                          # Enable UPX compression
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,                      # No console window
    disable_windowed_traceback=True,    # Disable traceback for security
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,             # Add code signing identity if available
    entitlements_file=None,
    icon=['E:\\Software Buid\\Final Meta Master With License\\Meta Master.ico'],
    version='version_info.txt',         # Add version info if available
    uac_admin=False,                    # Don't require admin privileges
    uac_uiaccess=False,
    manifest=None,
    embed_manifest=True,
    append_pkg=False,
    bootloader_ignore_signals=False,
)
